#!/bin/bash

# SSH配置自动化脚本
# 使用方法: ./setup_ssh.sh <remote_user> <remote_host> <ssh_port>

if [ $# -ne 3 ]; then
    echo "使用方法: $0 <远程用户名> <远程主机地址> <SSH端口>"
    exit 1
fi

REMOTE_USER=$1
REMOTE_HOST=$2
SSH_PORT=$3
KEY_NAME="$HOME/.ssh/id_rsa"

# 验证端口有效性
echo "$SSH_PORT" | grep -E "^[1-9][0-9]{0,4}$" > /dev/null
if [ $? -ne 0 ] || [ $SSH_PORT -lt 1 ] || [ $SSH_PORT -gt 65535 ]; then
    echo "无效的SSH端口: $SSH_PORT"
    exit 1
fi

# 调试信息
echo "当前用户主目录: $HOME"
echo "密钥将保存到: $KEY_NAME"
echo "使用SSH端口: $SSH_PORT"

# 1. 检查现有密钥
if [ -f "$KEY_NAME" ]; then
    echo "发现现有密钥: $KEY_NAME"
    read -p "是否覆盖现有密钥? (y/n) " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        rm -f "$KEY_NAME" "$KEY_NAME.pub" || { echo "无法删除现有密钥"; exit 1; }
    else
        exit 0
    fi
fi

# 2. 创建.ssh目录
echo "创建密钥存储目录..."
mkdir -p "$HOME/.ssh" || { echo "无法创建目录 $HOME/.ssh，请检查权限"; exit 1; }
chmod 700 "$HOME/.ssh" || { echo "无法设置目录权限"; exit 1; }

# 3. 生成新密钥
echo "生成新的SSH密钥..."
ssh-keygen -t rsa -b 4096 -f "$KEY_NAME" -N "" -C "auto-generated@yudao" 2>&1
if [ $? -ne 0 ]; then
    echo "密钥生成失败，错误详情："
    echo "请手动运行以下命令诊断："
    echo "1. ls -la $HOME/ | grep .ssh  # 检查目录权限"
    echo "2. df -h $HOME  # 检查磁盘空间"
    echo "3. touch $HOME/.ssh/test && rm $HOME/.ssh/test  # 测试文件操作权限"
    exit 1
fi

# 4. 上传公钥
if [ ! -f "${KEY_NAME}.pub" ]; then
    echo "公钥文件未生成: ${KEY_NAME}.pub"
    exit 1
fi

echo "上传公钥到 ${REMOTE_USER}@${REMOTE_HOST}:${SSH_PORT}..."
cat "${KEY_NAME}.pub" | ssh -p $SSH_PORT ${REMOTE_USER}@${REMOTE_HOST} "mkdir -p ~/.ssh && cat >> ~/.ssh/authorized_keys" 2>&1
if [ $? -ne 0 ]; then
    echo "公钥上传失败，错误详情："
    cat "${KEY_NAME}.pub" | ssh -p $SSH_PORT ${REMOTE_USER}@${REMOTE_HOST} "mkdir -p ~/.ssh && cat >> ~/.ssh/authorized_keys" 2>&1
    exit 1
fi

# 5. 设置权限
ssh -p $SSH_PORT ${REMOTE_USER}@${REMOTE_HOST} "chmod 700 ~/.ssh && chmod 600 ~/.ssh/authorized_keys" 2>&1
if [ $? -ne 0 ]; then
    echo "远程权限设置失败，错误详情："
    ssh -p $SSH_PORT ${REMOTE_USER}@${REMOTE_HOST} "chmod 700 ~/.ssh && chmod 600 ~/.ssh/authorized_keys" 2>&1
    exit 1
fi

chmod 600 "$KEY_NAME"
chmod 700 "$HOME/.ssh"

# 6. 测试连接
echo "测试SSH连接..."
ssh -i "$KEY_NAME" -p $SSH_PORT ${REMOTE_USER}@${REMOTE_HOST} "echo 'SSH配置成功'" 2>&1
if [ $? -ne 0 ]; then
    echo "SSH连接测试失败，错误详情："
    echo "请手动尝试以下命令诊断问题："
    echo "1. ssh -i $KEY_NAME -p $SSH_PORT ${REMOTE_USER}@${REMOTE_HOST}"
    echo "2. 检查服务器SSH配置: sudo grep 'Port' /etc/ssh/sshd_config"
    echo "3. 检查防火墙设置: sudo ufw status"
    exit 1
fi

echo "SSH配置已完成！"
echo "密钥文件路径: $KEY_NAME"
echo "测试连接命令: ssh -i $KEY_NAME -p $SSH_PORT ${REMOTE_USER}@${REMOTE_HOST}"