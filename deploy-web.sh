#!/bin/bash

# 配置信息
# 远程服务器信息
REMOTE_HOST="**************"
REMOTE_PORT="2233"
REMOTE_USER="root"
REMOTE_DIR="/data/web/nginx/project/coocaa"

# 构建环境，可选值: local, dev, test, stage, prod
BUILD_ENV="test"

# 部署模式，可选值: 1 (编译后部署), 2 (直接打包部署)
DEPLOY_MODE="1"

# 显示帮助信息
show_help() {
  echo "使用方法: $0 [选项]"
  echo "选项:"
  echo "  -h, --host     指定远程服务器IP地址 (默认: $REMOTE_HOST)"
  echo "  -p, --port     指定SSH端口 (默认: $REMOTE_PORT)"
  echo "  -u, --user     指定SSH用户名 (默认: $REMOTE_USER)"
  echo "  -d, --dir      指定远程部署目录 (默认: $REMOTE_DIR)"
  echo "  -e, --env      指定构建环境 (可选值: local, dev, test, stage, prod, 默认: $BUILD_ENV)"
  echo "  -m, --mode     指定部署模式 (1: 编译后部署, 2: 直接打包部署, 默认: $DEPLOY_MODE)"
  echo "  --help         显示此帮助信息"
  exit 0
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
  case "$1" in
    -h|--host)
      REMOTE_HOST="$2"
      shift 2
      ;;
    -p|--port)
      REMOTE_PORT="$2"
      shift 2
      ;;
    -u|--user)
      REMOTE_USER="$2"
      shift 2
      ;;
    -d|--dir)
      REMOTE_DIR="$2"
      shift 2
      ;;
    -e|--env)
      BUILD_ENV="$2"
      shift 2
      ;;
    -m|--mode)
      DEPLOY_MODE="$2"
      shift 2
      ;;
    --help)
      show_help
      ;;
    *)
      echo "未知选项: $1"
      show_help
      ;;
  esac
done

# 验证必要参数
if [ -z "$REMOTE_HOST" ] || [ -z "$REMOTE_USER" ] || [ -z "$REMOTE_DIR" ]; then
  echo "错误: 远程服务器信息不完整"
  show_help
fi

# 验证构建环境
if [[ ! "$BUILD_ENV" =~ ^(local|dev|test|stage|prod)$ ]]; then
  echo "错误: 无效的构建环境 '$BUILD_ENV'"
  echo "可选值: local, dev, test, stage, prod"
  exit 1
fi

# 验证部署模式
if [[ ! "$DEPLOY_MODE" =~ ^[12]$ ]]; then
  echo "错误: 无效的部署模式 '$DEPLOY_MODE'"
  echo "可选值: 1 (编译后部署), 2 (直接打包部署)"
  exit 1
fi

# 显示配置信息
echo "===== 部署配置 ====="
echo "远程服务器: $REMOTE_USER@$REMOTE_HOST:$REMOTE_PORT"
echo "远程目录: $REMOTE_DIR"
echo "构建环境: $BUILD_ENV"
if [ "$DEPLOY_MODE" = "1" ]; then
  echo "部署模式: 1 (编译后部署)"
else
  echo "部署模式: 2 (直接打包部署)"
fi
echo "===================="

# 确认部署
read -p "确认以上配置并开始部署? (y/n): " confirm
if [[ "$confirm" != "y" && "$confirm" != "Y" ]]; then
  echo "部署已取消"
  exit 0
fi

# 根据部署模式执行不同的构建流程
if [ "$DEPLOY_MODE" = "1" ]; then
  # 模式1: 编译后部署
  echo "===== 开始构建 ====="
  echo "正在清理缓存..."
  npm run clean:cache

  echo "正在构建项目 (环境: $BUILD_ENV)..."
  npm run build:$BUILD_ENV

  # 检查构建结果
  if [ $? -ne 0 ]; then
    echo "构建失败，请检查错误信息"
    exit 1
  fi
else
  # 模式2: 直接打包部署
  echo "===== 跳过构建步骤 ====="
  echo "使用现有构建文件进行部署..."
fi

# 获取构建输出目录
DIST_DIR="dist"
if [ ! -d "$DIST_DIR" ]; then
  echo "构建输出目录 '$DIST_DIR' 不存在，请检查构建配置"
  exit 1
fi

# 创建部署包
TIMESTAMP=$(date +"%Y%m%d%H%M%S")
PACKAGE_NAME="coocaa-web-$TIMESTAMP.tar.gz"

echo "正在创建部署包: $PACKAGE_NAME..."
tar -czf "$PACKAGE_NAME" -C "$DIST_DIR" .

# 上传到远程服务器
echo "===== 开始部署 ====="
echo "正在上传部署包到远程服务器..."
scp -P "$REMOTE_PORT" "$PACKAGE_NAME" "$REMOTE_USER@$REMOTE_HOST:/tmp/"

if [ $? -ne 0 ]; then
  echo "上传失败，请检查网络连接和服务器配置"
  rm -f "$PACKAGE_NAME"
  exit 1
fi

# 在远程服务器上解压部署包
echo "正在远程服务器上部署..."
ssh -p "$REMOTE_PORT" "$REMOTE_USER@$REMOTE_HOST" << EOF
  # 确保目标目录存在
  mkdir -p "$REMOTE_DIR"

  # 备份当前版本（如果存在）
  if [ -d "$REMOTE_DIR" ] && [ "\$(ls -A "$REMOTE_DIR" 2>/dev/null)" ]; then
    BACKUP_DIR="${REMOTE_DIR}_backup_\$(date +"%Y%m%d%H%M%S")"
    echo "备份当前版本到 \$BACKUP_DIR..."
    cp -r "$REMOTE_DIR" "\bak\$BACKUP_DIR"
  fi

  # 清空目标目录
  echo "清空目标目录..."
  rm -rf "$REMOTE_DIR"/*

  # 解压新版本
  echo "解压新版本..."
  tar -xzf "/tmp/$PACKAGE_NAME" -C "$REMOTE_DIR"

  # 清理临时文件
  rm -f "/tmp/$PACKAGE_NAME"

  echo "部署完成!"
EOF

# 清理本地临时文件
rm -f "$PACKAGE_NAME"

echo "===== 部署完成 ====="
echo "应用已成功部署到 $REMOTE_USER@$REMOTE_HOST:$REMOTE_DIR"
